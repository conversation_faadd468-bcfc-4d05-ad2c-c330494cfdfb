"use client";

import React, { useState } from "react";
import { products } from "@/constants/products";
import ProductDisplay from "@/components/product/ProductDisplay";
import Story from "@/components/product/story";
import StoryTiles from "@/components/product/storyTiles";
import Ete from "@/components/home/<USER>";
import ProductGallery from "@/components/product/productGallery";

export default function Page() {
  const [selectedProduct, setSelectedProduct] = useState(products[0]);

  return (
    <>
      <ProductDisplay
        product={selectedProduct}
        onProductChange={setSelectedProduct}
      />
      <Story />
      <StoryTiles selectedProduct={selectedProduct} />
      <ProductGallery selectedProduct={selectedProduct} />
      <Ete />
    </>
  );
}
