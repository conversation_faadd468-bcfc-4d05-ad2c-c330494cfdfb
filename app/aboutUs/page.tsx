"use client";

import React, { useState } from "react";
import <PERSON> from "@/components/aboutUs/hero";
import Timeline from "@/components/aboutUs/timeline";
import Ete from "@/components/home/<USER>";
import ImageSection from "@/components/aboutUs/imageSection";
import { TabData } from "@/constants/aboutUs/heroData";

export default function Page() {
  const [selectedTab, setSelectedTab] = useState<TabData | null>(null);

  const handleTabChange = (tab: TabData) => {
    setSelectedTab(tab);
  };

  // Check if the current tab is "Prestige" (adjust the condition based on your tab structure)
  const showImageSection =
    selectedTab?.name?.toLowerCase() === "prestige" ||
    selectedTab?.id === "prestige";

  return (
    <>
      <Hero onTabChange={handleTabChange} />

      {/* Show ImageSection only when Prestige tab is selected */}
      {showImageSection && (
        <ImageSection
          largeImageUrl="/path/to/prestige-large-image.jpg"
          smallImageUrl="/path/to/prestige-small-image.jpg"
          title="Prestige"
        />
      )}

      <Timeline />
      <Ete />
    </>
  );
}
