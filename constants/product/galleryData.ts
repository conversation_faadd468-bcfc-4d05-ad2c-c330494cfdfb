import { images } from "@/constants/images";

export type GalleryItem = {
  id: number;
  imgUrl: string;
  size: "small" | "medium" | "large" | "tall" | "wide";
  title: string;
};

export type ProductGalleryData = {
  [productName: string]: GalleryItem[];
};

export const productGalleryData: ProductGalleryData = {
  RINGS: [
    {
      id: 1,
      imgUrl: images.s1.src,
      size: "large",
      title: "Elegant Ring Collection",
    },
    {
      id: 2,
      imgUrl: images.crafting.src,
      size: "medium",
      title: "Ring Crafting Process",
    },
    {
      id: 3,
      imgUrl: images.Excellence.src,
      size: "small",
      title: "Premium Materials",
    },
    {
      id: 4,
      imgUrl: images.Experience.src,
      size: "tall",
      title: "Custom Design Experience",
    },
    {
      id: 5,
      imgUrl: images.s3.src,
      size: "wide",
      title: "Ring Details & Finishes",
    },
    {
      id: 6,
      imgUrl: images.hero.src,
      size: "medium",
      title: "Wedding Rings",
    },
    {
      id: 7,
      imgUrl: images.Transparency.src,
      size: "small",
      title: "Ethical Sourcing",
    },
    {
      id: 8,
      imgUrl: images.overlay.src,
      size: "medium",
      title: "Ring Sizing Guide",
    },
    {
      id: 9,
      imgUrl: images.overlay.src,
      size: "tall",
      title: "Ring Sizing Guide",
    },
    {
      id: 10,
      imgUrl: images.overlay.src,
      size: "large",
      title: "Ring Sizing Guide",
    },
  ],

  EARRINGS: [
    {
      id: 1,
      imgUrl: images.s1.src,
      size: "large",
      title: "Earring Styles",
    },
    {
      id: 2,
      imgUrl: images.crafting.src,
      size: "medium",
      title: "Precision Setting",
    },
    {
      id: 3,
      imgUrl: images.Excellence.src,
      size: "tall",
      title: "Hypoallergenic Materials",
    },
    {
      id: 4,
      imgUrl: images.s3.src,
      size: "small",
      title: "Stud Variations",
    },
    {
      id: 5,
      imgUrl: images.hero.src,
      size: "wide",
      title: "Drop & Dangle Collection",
    },
    {
      id: 6,
      imgUrl: images.Experience.src,
      size: "medium",
      title: "Comfort Fitting",
    },
    {
      id: 7,
      imgUrl: images.overlay.src,
      size: "small",
      title: "Secure Backings",
    },
    {
      id: 8,
      imgUrl: images.Transparency.src,
      size: "medium",
      title: "Size Guide",
    },
    {
      id: 9,
      imgUrl: images.overlay.src,
      size: "tall",
      title: "Ring Sizing Guide",
    },
    {
      id: 10,
      imgUrl: images.overlay.src,
      size: "large",
      title: "Ring Sizing Guide",
    },
  ],

  NECKLACES: [
    {
      id: 1,
      imgUrl: images.hero.src,
      size: "large",
      title: "Necklace Collections",
    },
    {
      id: 2,
      imgUrl: images.s3.src,
      size: "tall",
      title: "Chain Details",
    },
    {
      id: 3,
      imgUrl: images.crafting.src,
      size: "medium",
      title: "Pendant Creation",
    },
    {
      id: 4,
      imgUrl: images.Excellence.src,
      size: "small",
      title: "Quality Clasps",
    },
    {
      id: 5,
      imgUrl: images.overlay.src,
      size: "wide",
      title: "Layering Styles",
    },
    {
      id: 6,
      imgUrl: images.Experience.src,
      size: "medium",
      title: "Personal Consultation",
    },
    {
      id: 7,
      imgUrl: images.s1.src,
      size: "small",
      title: "Custom Engraving",
    },
    {
      id: 8,
      imgUrl: images.Transparency.src,
      size: "medium",
      title: "Care Instructions",
    },
    {
      id: 9,
      imgUrl: images.overlay.src,
      size: "tall",
      title: "Ring Sizing Guide",
    },
    {
      id: 10,
      imgUrl: images.overlay.src,
      size: "large",
      title: "Ring Sizing Guide",
    },
  ],

  PENDANTS: [
    {
      id: 1,
      imgUrl: images.hero.src,
      size: "large",
      title: "Necklace Collections",
    },
    {
      id: 2,
      imgUrl: images.s3.src,
      size: "tall",
      title: "Chain Details",
    },
    {
      id: 3,
      imgUrl: images.crafting.src,
      size: "medium",
      title: "Pendant Creation",
    },
    {
      id: 4,
      imgUrl: images.Excellence.src,
      size: "small",
      title: "Quality Clasps",
    },
    {
      id: 5,
      imgUrl: images.overlay.src,
      size: "wide",
      title: "Layering Styles",
    },
    {
      id: 6,
      imgUrl: images.Experience.src,
      size: "medium",
      title: "Personal Consultation",
    },
    {
      id: 7,
      imgUrl: images.s1.src,
      size: "small",
      title: "Custom Engraving",
    },
    {
      id: 8,
      imgUrl: images.Transparency.src,
      size: "medium",
      title: "Care Instructions",
    },
    {
      id: 9,
      imgUrl: images.overlay.src,
      size: "tall",
      title: "Ring Sizing Guide",
    },
    {
      id: 10,
      imgUrl: images.overlay.src,
      size: "large",
      title: "Ring Sizing Guide",
    },
  ],

  BRACELETS: [
    {
      id: 1,
      imgUrl: images.overlay.src,
      size: "large",
      title: "Bracelet Collection",
    },
    {
      id: 2,
      imgUrl: images.s3.src,
      size: "medium",
      title: "Link Patterns",
    },
    {
      id: 3,
      imgUrl: images.crafting.src,
      size: "tall",
      title: "Bracelet Assembly",
    },
    {
      id: 4,
      imgUrl: images.Excellence.src,
      size: "small",
      title: "Durable Clasps",
    },
    {
      id: 5,
      imgUrl: images.hero.src,
      size: "wide",
      title: "Tennis & Chain Bracelets",
    },
    {
      id: 6,
      imgUrl: images.s1.src,
      size: "medium",
      title: "Custom Sizing",
    },
    {
      id: 7,
      imgUrl: images.Experience.src,
      size: "small",
      title: "Comfort Design",
    },
    {
      id: 8,
      imgUrl: images.Transparency.src,
      size: "medium",
      title: "Maintenance Tips",
    },
    {
      id: 9,
      imgUrl: images.overlay.src,
      size: "tall",
      title: "Ring Sizing Guide",
    },
    {
      id: 10,
      imgUrl: images.overlay.src,
      size: "large",
      title: "Ring Sizing Guide",
    },
  ],

  BANGLES: [
    {
      id: 1,
      imgUrl: images.hero.src,
      size: "large",
      title: "Necklace Collections",
    },
    {
      id: 2,
      imgUrl: images.s3.src,
      size: "tall",
      title: "Chain Details",
    },
    {
      id: 3,
      imgUrl: images.crafting.src,
      size: "medium",
      title: "Pendant Creation",
    },
    {
      id: 4,
      imgUrl: images.Excellence.src,
      size: "small",
      title: "Quality Clasps",
    },
    {
      id: 5,
      imgUrl: images.overlay.src,
      size: "wide",
      title: "Layering Styles",
    },
    {
      id: 6,
      imgUrl: images.Experience.src,
      size: "medium",
      title: "Personal Consultation",
    },
    {
      id: 7,
      imgUrl: images.s1.src,
      size: "small",
      title: "Custom Engraving",
    },
    {
      id: 8,
      imgUrl: images.Transparency.src,
      size: "medium",
      title: "Care Instructions",
    },
    {
      id: 9,
      imgUrl: images.overlay.src,
      size: "tall",
      title: "Ring Sizing Guide",
    },
    {
      id: 10,
      imgUrl: images.overlay.src,
      size: "large",
      title: "Ring Sizing Guide",
    },
  ],
};

// Helper function to get gallery data for a product
export const getProductGallery = (productName: string): GalleryItem[] => {
  return productGalleryData[productName.toUpperCase()];
};
