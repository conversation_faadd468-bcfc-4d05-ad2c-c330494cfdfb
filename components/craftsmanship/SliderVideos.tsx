"use client";

import React, { useState, useEffect, useRef } from "react";
import { useDrag } from "@use-gesture/react";
import Image from "next/image";
import { images } from "@/constants/images";

const imageSlides = [
  {
    src: images.crafting.src,
    alt: "Jewelry crafting process - left view",
  },
  {
    src: images.Experience.src,
    alt: "Jewelry crafting process - center view",
  },
  {
    src: images.overlay.src,
    alt: "Jewelry crafting process - right view",
  },
  {
    src: images.Transparency.src,
    alt: "Jewelry crafting process - right view",
  },
];

export default function EnhancedImageSlider() {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isMobile, setIsMobile] = useState(false);
  const [containerWidth, setContainerWidth] = useState(0);
  const sliderContainerRef = useRef<HTMLDivElement>(null);
  const totalSlides = imageSlides.length;

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  // Measure container width
  useEffect(() => {
    const observer = new ResizeObserver((entries) => {
      for (const entry of entries) {
        setContainerWidth(entry.contentRect.width);
      }
    });

    if (sliderContainerRef.current) {
      observer.observe(sliderContainerRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const changeSlide = (newIndex: number) => {
    setCurrentSlide(((newIndex % totalSlides) + totalSlides) % totalSlides);
  };

  const getDesktopSlideStyles = (index: number) => {
    if (index === currentSlide) {
      return "w-full md:w-[45%] lg:w-[38%] xl:w-[34%] h-full z-10 opacity-100 scale-100 shadow-2xl";
    } else if (index === (currentSlide - 1 + totalSlides) % totalSlides) {
      return "w-[30%] md:w-[26%] lg:w-[22%] xl:w-[20%] h-[75%] md:h-[80%] z-0 opacity-50 scale-[0.85] transform -translate-x-[75%] md:-translate-x-[65%] lg:-translate-x-[100%] hover:opacity-70 hover:scale-[0.90] cursor-pointer";
    } else if (index === (currentSlide + 1) % totalSlides) {
      return "w-[30%] md:w-[26%] lg:w-[22%] xl:w-[20%] h-[75%] md:h-[80%] z-0 opacity-50 scale-[0.85] transform translate-x-[75%] md:translate-x-[65%] lg:translate-x-[100%] hover:opacity-70 hover:scale-[0.90] cursor-pointer";
    }
    return "hidden";
  };

  // Mobile drag implementation
  const [dragState, setDragState] = useState({ x: 0, active: false });

  const bind = useDrag(
    ({ active, movement: [mx], velocity: [vx] }) => {
      if (active) {
        setDragState({ x: mx, active: true });
      } else {
        setDragState({ x: 0, active: false });
        const threshold = containerWidth / 4;
        const velocityThreshold = 0.3;

        if (Math.abs(mx) > threshold || Math.abs(vx) > velocityThreshold) {
          changeSlide(
            currentSlide + (mx < 0 || vx < -velocityThreshold ? 1 : -1),
          );
        }
      }
    },
    {
      axis: "x",
      filterTaps: true,
      preventScroll: true,
    },
  );

  // Calculate mobile transform
  const mobileTransform = () => {
    const baseTranslate = -currentSlide * containerWidth;
    return `translateX(${baseTranslate + dragState.x}px)`;
  };

  return (
    <div className="bg-gray-100">
      <div className="py-10 md:py-16">
        <div className="mb-10 px-4 text-center md:mb-12">
          <h4 className="text-sm font-semibold tracking-wider text-gray-600 uppercase">
            Coachbuild
          </h4>
          <h2 className="mt-2 text-3xl font-bold text-gray-900 md:text-4xl">
            FROM VISION TO REALITY
          </h2>
        </div>

        {/* Image slider container - This will be the draggable area */}
        <div
          ref={sliderContainerRef}
          {...bind()}
          className={`relative w-full cursor-pointer items-center justify-center overflow-hidden select-none ${isMobile ? "h-[280px] sm:h-[340px]" : "flex h-[280px] sm:h-[340px] md:h-[420px] lg:h-[500px] xl:h-[580px]"}`}
          style={{ touchAction: "pan-y" }} // Allow vertical scroll if needed, but prioritize horizontal pan
        >
          {isMobile ? (
            <div
              className="flex h-full"
              style={{
                width: `${totalSlides * containerWidth}px`,
                transform: mobileTransform(),
                transition: dragState.active
                  ? "none"
                  : "transform 0.5s ease-out",
              }}
            >
              {imageSlides.map((slide, index) => (
                <div
                  key={index}
                  className="h-full flex-shrink-0 px-1"
                  style={{ width: `${100 / totalSlides}%` }}
                >
                  <div className="h-full w-full overflow-hidden rounded-lg shadow-xl">
                    <Image
                      src={slide.src}
                      alt={slide.alt}
                      width={400}
                      height={300}
                      priority={index === currentSlide}
                      className="pointer-events-none h-full w-full object-cover"
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                      draggable={false}
                    />
                  </div>
                </div>
              ))}
            </div>
          ) : (
            // Desktop: Existing absolute positioning for 3-up display
            imageSlides.map((slide, index) => (
              <div
                key={index}
                className={`absolute transition-all duration-700 ease-in-out ${getDesktopSlideStyles(
                  index,
                )}`}
                onClick={() => {
                  if (index !== currentSlide && !isMobile) {
                    // Allow clicking side slides to navigate only if they are the direct prev/next
                    const prevActual =
                      (currentSlide - 1 + totalSlides) % totalSlides;
                    const nextActual = (currentSlide + 1) % totalSlides;
                    if (index === prevActual || index === nextActual) {
                      changeSlide(index);
                    }
                  }
                }}
              >
                <div className="h-full w-full overflow-hidden rounded-lg shadow-xl">
                  <Image
                    src={slide.src}
                    alt={slide.alt}
                    width={400}
                    height={300}
                    priority={index === currentSlide}
                    className="pointer-events-none h-full w-full object-cover"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    draggable={false}
                  />
                </div>
              </div>
            ))
          )}
        </div>

        {/* Navigation dots */}
        <div className="mt-10 flex justify-center space-x-3 md:mt-12">
          {imageSlides.map((_, index) => (
            <button
              key={index}
              onClick={() => changeSlide(index)}
              className={`h-3 w-3 rounded-full transition-all duration-300 ease-in-out focus:ring-2 focus:ring-rose-300 focus:ring-offset-2 focus:outline-none ${
                index === currentSlide
                  ? "w-10 scale-110 bg-rose-400"
                  : "bg-rose-700"
              }`}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
