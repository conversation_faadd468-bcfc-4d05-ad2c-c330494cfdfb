"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "motion/react";
import Image from "next/image";
import { ChevronDown } from "lucide-react";
import BorderedSection from "@/components/ui/borderedSection";
import { TabData, tabsData } from "@/constants/aboutUs/heroData";
import BrandFilm from "@/components/home/<USER>";
import SliderVideos from "@/components/craftsmanship/SliderVideos";
import VideoPlayer from "@/components/videoPlayer";
import { videos } from "@/constants/video";
import { images } from "@/constants/images";

// Mobile Tabs Component
function MobileTabs({
  currentTab,
  onSelectTab,
}: {
  currentTab: TabData;
  onSelectTab: (tab: TabData) => void;
}) {
  const [menuOpen, setMenuOpen] = useState(false);

  const closeMenu = () => {
    setMenuOpen(false);
  };

  const containerVariants = {
    hidden: { opacity: 0, height: 0 },
    visible: {
      opacity: 1,
      height: "auto",
      transition: {
        duration: 0.3,
        when: "beforeChildren",
        staggerChildren: 0.1,
      },
    },
    exit: {
      opacity: 0,
      height: 0,
      transition: {
        duration: 0.3,
        when: "afterChildren",
        staggerChildren: 0.05,
        staggerDirection: -1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.3 } },
    exit: { opacity: 0, y: -20, transition: { duration: 0.2 } },
  };

  return (
    <div className="absolute top-16 right-0 left-0 z-30 lg:hidden">
      {/* Header Bar */}
      <div className="flex h-16 items-center justify-between bg-transparent px-6">
        <div className="flex items-center">
          <button
            onClick={() => setMenuOpen(!menuOpen)}
            className="flex items-center justify-center text-sm tracking-widest text-white"
          >
            {currentTab.name}
            <motion.div
              animate={{ rotate: menuOpen ? 180 : 0 }}
              transition={{ duration: 0.3 }}
            >
              <ChevronDown size={18} color="white" />
            </motion.div>
          </button>
        </div>
      </div>

      {/* Dropdown Menu */}
      <AnimatePresence>
        {menuOpen && (
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            className="w-full overflow-hidden shadow-lg backdrop-blur-md"
          >
            {tabsData.map((tab, index) => (
              <motion.div key={tab.id} variants={itemVariants} custom={index}>
                <button
                  onClick={() => {
                    onSelectTab(tab);
                    closeMenu();
                  }}
                  className={`flex w-full items-center px-6 py-4 text-left text-sm tracking-wider transition-colors duration-300 ${
                    tab.id === currentTab.id
                      ? "bg-white/10 text-white"
                      : "text-white/70 hover:bg-white/5 hover:text-white"
                  }`}
                >
                  {tab.name}
                </button>
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Overlay */}
      <AnimatePresence>
        {menuOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="fixed inset-0 z-[-1] bg-black/20 backdrop-blur-sm"
            onClick={closeMenu}
          />
        )}
      </AnimatePresence>
    </div>
  );
}

export default function Hero() {
  const [selectedTab, setSelectedTab] = useState<TabData>(tabsData[0]);

  const handleTabSelect = (tab: TabData) => {
    setSelectedTab(tab);
  };

  return (
    <section className="overflow-hidden">
      <div className="relative h-screen w-screen overflow-hidden">
        {/* Mobile Tabs Component */}
        <MobileTabs currentTab={selectedTab} onSelectTab={handleTabSelect} />

        {/* Desktop Navigation */}
        <nav className="absolute top-16 right-0 left-0 z-10 hidden px-16 pt-8 lg:flex lg:justify-between">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
            className="text-sm font-light tracking-widest text-white uppercase"
          >
            Jewels of Venus
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, staggerChildren: 0.1 }}
            className="flex space-x-10"
          >
            {tabsData.map((tab, index) => (
              <motion.button
                key={tab.id}
                onClick={() => handleTabSelect(tab)}
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="relative cursor-pointer text-xs tracking-widest uppercase"
              >
                <span
                  className={`${
                    tab.id === selectedTab.id
                      ? "text-white"
                      : "text-white/70 transition-colors duration-300 hover:text-[#e7ddd1]"
                  }`}
                >
                  {tab.name}
                </span>
                {tab.id === selectedTab.id && (
                  <motion.div
                    className="absolute -bottom-1 left-0 h-px w-full bg-white"
                    layoutId="underline"
                    transition={{ duration: 0.3, ease: "easeInOut" }}
                  />
                )}
              </motion.button>
            ))}
          </motion.div>
        </nav>

        {/* Main Content */}
        <motion.div
          className="relative h-full w-full"
          key={selectedTab.id}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1 }}
        >
          {/* Background Image */}
          <Image
            src={selectedTab.imageUrl}
            alt={selectedTab.title}
            width={1920}
            height={1080}
            className="h-full w-full object-cover object-center brightness-75"
            priority
          />

          {/* Content Overlay */}
          <div className="absolute inset-0 flex flex-col items-center justify-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="text-center"
            >
              <motion.h1
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="mb-2 text-4xl font-light tracking-widest text-white uppercase md:text-7xl"
              >
                {selectedTab.title}
              </motion.h1>
              <motion.div
                initial={{ width: 0 }}
                animate={{ width: "8rem" }}
                transition={{ duration: 0.8, delay: 0.4 }}
                className="mx-auto mb-6 h-px bg-white/50"
              />
              <motion.h2
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.8, delay: 0.6 }}
                className="text-sm tracking-widest text-white/80 uppercase"
              >
                {selectedTab.subtitle}
              </motion.h2>
            </motion.div>
          </div>
        </motion.div>
      </div>

      {/* Image Section - Only show for Prestige tab */}
      {selectedTab.id === "prestige" &&
        selectedTab.largeImageUrl &&
        selectedTab.smallImageUrl && (
          <section className="py-16 lg:py-24">
            <div className="container mx-auto px-6 lg:px-16">
              <div className="grid grid-cols-1 lg:grid-cols-2">
                {/* Large Image */}
                <motion.div
                  initial={{ opacity: 0, x: -30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8 }}
                  viewport={{ once: true }}
                  className="relative aspect-[3/4] overflow-hidden"
                >
                  <Image
                    src={selectedTab.largeImageUrl}
                    alt={`${selectedTab.title} detail view`}
                    fill
                    className="object-cover object-center"
                  />
                </motion.div>

                {/* Small Image */}
                <motion.div
                  initial={{ opacity: 0, x: 30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.2 }}
                  viewport={{ once: true }}
                  className="relative aspect-[3/2] overflow-hidden"
                >
                  <Image
                    src={selectedTab.smallImageUrl}
                    alt={`${selectedTab.title} close-up view`}
                    fill
                    className="object-cover object-center"
                  />
                </motion.div>
              </div>
            </div>
          </section>
        )}

      {/* Description Section */}
      <BorderedSection
        text={selectedTab.description}
        highlightWords={selectedTab.highlightWords}
      />

      {/* Conditional Components for Precision tab */}
      {selectedTab.id === "precision" && (
        <>
          <BrandFilm />
          <SliderVideos />
        </>
      )}

      {/* Conditional Components for Perfection tab */}
      {selectedTab.id === "perfection" && selectedTab.perfectionImages && (
        <div className="relative h-[65vh] w-full overflow-hidden p-4 md:h-[80vh] lg:h-screen">
          {/* First image - concept design */}
          <motion.div
            className="absolute bottom-40 left-[2vw] z-10 h-[20vh] w-[35vw] md:bottom-20 md:h-[30vh] lg:bottom-[11vh] lg:h-[52vh] lg:w-[35vw]"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.1 }}
          >
            <Image
              src={selectedTab.perfectionImages.concept}
              alt="Concept Design"
              fill
              className="object-cover"
            />
          </motion.div>

          {/* Second image - craftsman working */}
          <motion.div
            className="absolute top-32 left-[23vw] z-20 h-[40vh] w-[35vw] md:h-[60vh] lg:top-0 lg:left-[32vw] lg:h-screen lg:w-[25vw]"
            initial={{ opacity: 0, y: -50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
          >
            <Image
              src={selectedTab.perfectionImages.craftsman}
              alt="Craftsman Working"
              fill
              className="object-cover"
            />
          </motion.div>

          {/* Third image - red material */}
          <motion.div
            className="absolute right-[23vw] bottom-[13vh] z-30 h-[20vh] w-[30vw] md:h-[34vh] md:w-[30vw] lg:bottom-[2vh] lg:h-[47vh] lg:w-[23vw]"
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.5 }}
          >
            <Image
              src={selectedTab.perfectionImages.material}
              alt="Material"
              fill
              className="object-cover"
            />
          </motion.div>

          {/* Fourth image - product detail */}
          <motion.div
            className="absolute top-[25vh] right-[2vw] z-20 h-[20vh] w-[36vw] md:top-[20vh] md:h-[40vh] lg:top-[9vh] lg:h-[59vh] lg:w-[27vw]"
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.7 }}
          >
            <Image
              src={selectedTab.perfectionImages.detail}
              alt="Product Detail"
              fill
              className="object-cover"
            />
          </motion.div>
        </div>
      )}

      {/* Values  for Perfection tab */}
      {selectedTab.id === "perfection" && (
     
      )}

      {/* Campaign film for Perfection tab */}
      {selectedTab.id === "perfection" && (
        <div className="relative h-screen w-full overflow-hidden">
          <VideoPlayer
            src={videos.hero.src}
            alt={videos.hero.alt}
            autoPlay={true}
            muted={true}
            loop
            controls="none"
            objectFit="cover"
            priority
            className="h-full w-full object-cover"
          />
        </div>
      )}
    </section>
  );
}
