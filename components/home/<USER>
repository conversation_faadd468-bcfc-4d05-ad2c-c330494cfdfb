"use client";

import React from "react";
import { AuroraBackground } from "@/components/ui/aurora-background";
import { motion } from "motion/react";
import { videos } from "@/constants/video";
import VideoPlayer from "@/components/videoPlayer";

export default function Hero() {
  return (
    <AuroraBackground className="relative h-screen w-full overflow-hidden">
      {/* Video background */}
      <motion.div
        initial={{ opacity: 0, y: 40 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{
          delay: 0.3,
          duration: 0.8,
          ease: "easeInOut",
        }}
        className="relative z-0 h-full w-full"
      >
        <VideoPlayer
          src={videos.hero.src}
          alt={videos.hero.alt}
          autoPlay={true}
          muted={true}
          loop
          controls="none"
          objectFit="cover"
          priority
          className="absolute inset-0"
        />
      </motion.div>
    </AuroraBackground>
  );
}
