import React from "react";
import { motion } from "motion/react";

const containerVariants = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.3,
      delayChildren: 0.4,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 30 },
  show: { opacity: 1, y: 0, transition: { duration: 0.8, ease: "easeOut" } },
};

export default function Solitaire() {
  return (
    <div className="flex h-screen items-center justify-center bg-[#020344] px-6">
      <motion.section
        variants={containerVariants}
        initial="hidden"
        whileInView="show"
        viewport={{ once: true }}
        className="max-w-5xl text-center"
      >
        <motion.p
          variants={itemVariants}
          className="mb-6 text-xl tracking-tight text-white sm:text-2xl md:text-4xl"
        >
          A Solitaire Jewellery Experience, Like Never Before
        </motion.p>
        <motion.p
          variants={itemVariants}
          className="text-lg leading-relaxed text-white/80 md:text-2xl"
        >
          Welcome to Jewels of Venus – a new era of solitaire brilliance. Born
          from a legacy of excellence, we craft solitaires that go beyond
          jewellery — we capture stories, moments, and emotions. With precision,
          passion, and innovation, we redefine elegance, offering natural
          diamonds that shine as uniquely as the ones who wear them.
        </motion.p>
      </motion.section>
    </div>
  );
}
