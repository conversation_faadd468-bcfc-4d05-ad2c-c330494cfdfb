import { useState } from "react";
import { motion, AnimatePresence } from "motion/react";
import { ChevronDown } from "lucide-react";

export interface TabItem {
  id: string;
  name: string;
  [key: string]: any;
}

interface ReusableTabsProps<T extends TabItem> {
  items: T[];
  currentItem: T;
  onSelectItem: (item: T) => void;
  mobileButtonText?: string;
  brandName?: string;
  showDesktopNav?: boolean;
  showMobileNav?: boolean;
  className?: string;
  mobileClassName?: string;
  desktopClassName?: string;
}

export default function ReusableTabs<T extends TabItem>({
  items,
  currentItem,
  onSelectItem,
  mobileButtonText,
  brandName,
  showDesktopNav = true,
  showMobileNav = true,
  className = "",
  mobileClassName = "",
  desktopClassName = "",
}: ReusableTabsProps<T>) {
  const [menuOpen, setMenuOpen] = useState(false);

  const closeMenu = () => {
    setMenuOpen(false);
  };

  const containerVariants = {
    hidden: { opacity: 0, height: 0 },
    visible: {
      opacity: 1,
      height: "auto",
      transition: {
        duration: 0.3,
        when: "beforeChildren",
        staggerChildren: 0.1,
      },
    },
    exit: {
      opacity: 0,
      height: 0,
      transition: {
        duration: 0.3,
        when: "afterChildren",
        staggerChildren: 0.05,
        staggerDirection: -1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.3 } },
    exit: { opacity: 0, y: -20, transition: { duration: 0.2 } },
  };

  return (
    <>
      {/* Mobile Navigation */}
      {showMobileNav && (
        <div
          className={`absolute top-16 right-0 left-0 z-30 lg:hidden ${mobileClassName}`}
        >
          {/* Header Bar */}
          <div className="flex h-16 items-center justify-between bg-transparent px-6">
            <div className="flex items-center">
              <button
                onClick={() => setMenuOpen(!menuOpen)}
                className="flex items-center justify-center text-sm tracking-widest text-white"
              >
                {mobileButtonText || currentItem.name}
                <motion.div
                  animate={{ rotate: menuOpen ? 180 : 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <ChevronDown size={18} color="white" />
                </motion.div>
              </button>
            </div>
          </div>

          {/* Dropdown Menu */}
          <AnimatePresence>
            {menuOpen && (
              <motion.div
                variants={containerVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
                className="w-full overflow-hidden shadow-lg backdrop-blur-md"
              >
                {items.map((item, index) => (
                  <motion.div
                    key={item.id}
                    variants={itemVariants}
                    custom={index}
                  >
                    <button
                      onClick={() => {
                        onSelectItem(item);
                        closeMenu();
                      }}
                      className={`flex w-full items-center px-6 py-4 text-left text-sm tracking-wider transition-colors duration-300 ${
                        item.id === currentItem.id
                          ? "bg-white/10 text-white"
                          : "text-white/70 hover:bg-white/5 hover:text-white"
                      }`}
                    >
                      {item.name}
                    </button>
                  </motion.div>
                ))}
              </motion.div>
            )}
          </AnimatePresence>

          {/* Overlay */}
          <AnimatePresence>
            {menuOpen && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="fixed inset-0 z-[-1] bg-black/20 backdrop-blur-sm"
                onClick={closeMenu}
              />
            )}
          </AnimatePresence>
        </div>
      )}

      {/* Desktop Navigation */}
      {showDesktopNav && (
        <nav
          className={`absolute top-16 right-0 left-0 z-10 hidden px-16 pt-8 lg:flex lg:justify-between ${desktopClassName}`}
        >
          {brandName && (
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
              className="text-sm font-light tracking-widest text-white uppercase"
            >
              {brandName}
            </motion.div>
          )}

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, staggerChildren: 0.1 }}
            className="flex space-x-10"
          >
            {items.map((item, index) => (
              <motion.button
                key={item.id}
                onClick={() => onSelectItem(item)}
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="relative cursor-pointer text-xs tracking-widest uppercase"
              >
                <span
                  className={`${
                    item.id === currentItem.id
                      ? "text-white"
                      : "text-white/70 transition-colors duration-300 hover:text-[#e7ddd1]"
                  }`}
                >
                  {item.name}
                </span>
                {item.id === currentItem.id && (
                  <motion.div
                    className="absolute -bottom-1 left-0 h-px w-full bg-white"
                    layoutId="underline"
                    transition={{ duration: 0.3, ease: "easeInOut" }}
                  />
                )}
              </motion.button>
            ))}
          </motion.div>
        </nav>
      )}
    </>
  );
}
