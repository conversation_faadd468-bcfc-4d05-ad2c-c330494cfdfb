"use client";

import React, { useRef, useState, useEffect } from "react";
import {
  motion,
  useScroll,
  useTransform,
  useSpring,
  MotionValue,
} from "motion/react";
import { cn } from "@/lib/utils";
import HighlightedText from "@/components/ui/HighlightedText";
import { IconArrowRight } from "@tabler/icons-react";

interface ContentItem {
  title: string;
  subtitle?: string;
  description: string;
  image?: string;
  highlightWords?: string[];
  alignment?: "left" | "right";
  content?: React.ReactNode;
  buttonText?: string;
  buttonLink?: string;
}

interface ParallaxScrollProps {
  content: ContentItem[];
  contentClassName?: string;
}

const ContentItem = ({
  index,
  item,
  windowHeight,
  smoothScrollProgress,
  contentLength,
}: {
  index: number;
  item: ContentItem;
  windowHeight: number;
  smoothScrollProgress: MotionValue<number>;
  contentLength: number;
}) => {
  const contentY = useTransform(
    smoothScrollProgress,
    [index / contentLength, (index + 1) / contentLength],
    [windowHeight * 0.1, -windowHeight * 0.1],
  );

  const isEven = index % 2 === 0;
  const alignment = item.alignment || (isEven ? "left" : "right");

  const handleButtonClick = () => {
    if (item.buttonLink) {
      // For internal section navigation
      const element = document.querySelector(item.buttonLink);
      if (element) {
        element.scrollIntoView({ behavior: "smooth" });
      } else {
        window.location.href = item.buttonLink;
      }
    }
  };

  return (
    <div
      className="absolute h-screen w-full"
      style={{ top: `${index * 100}vh` }}
    >
      {/* Background */}
      <div className="absolute inset-0 h-full w-full">
        {item.image ? (
          <div
            className="absolute inset-0 bg-cover bg-center bg-no-repeat"
            style={{ backgroundImage: `url(${item.image})` }}
          />
        ) : item.content ? (
          <div className="absolute inset-0">{item.content}</div>
        ) : (
          <div className="absolute inset-0 bg-gradient-to-br from-indigo-900 to-blue-950" />
        )}
      </div>

      {/* Foreground motion content */}
      <motion.div
        className={cn(
          "absolute inset-0 z-10 flex items-center",
          alignment === "left" ? "justify-start" : "justify-end",
        )}
        style={{ y: contentY }}
      >
        <div
          className={cn(
            "max-w-md space-y-6 p-8",
            alignment === "left" ? "ml-8 md:ml-16" : "mr-8 md:mr-16",
          )}
        >
          <h2 className="font-display text-3xl font-bold tracking-wider text-white uppercase md:text-5xl">
            {item.title}
          </h2>
          {item.subtitle && (
            <h3 className="font-display text-xl tracking-wide text-white/80 uppercase md:text-2xl">
              {item.subtitle}
            </h3>
          )}
          <div className="text-md max-w-sm text-slate-200 md:text-lg">
            <HighlightedText
              text={item.description}
              highlightWords={item.highlightWords || []}
              strokeColor="#ffffff"
            />
          </div>

          {/* Know More Button */}
          {item.buttonText && item.buttonLink && (
            <motion.button
              onClick={handleButtonClick}
              className="group relative overflow-hidden rounded-lg border border-white/30 bg-white/10 px-8 py-3 font-semibold text-white backdrop-blur-sm transition-all duration-300 hover:border-white/50 hover:bg-white/20"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <span className="relative z-10 flex items-center gap-2 text-sm tracking-wider uppercase">
                {item.buttonText}
                <motion.div
                  className="inline-flex"
                  initial={{ x: 0 }}
                  whileHover={{ x: 5 }}
                  transition={{ duration: 0.2 }}
                >
                  <IconArrowRight size={16} />
                </motion.div>
              </span>
              <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/5 to-white/0 opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
            </motion.button>
          )}
        </div>
      </motion.div>
    </div>
  );
};

export const ParallaxScroll = ({
  content,
  contentClassName,
}: ParallaxScrollProps) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [windowHeight, setWindowHeight] = useState(0);

  useEffect(() => {
    const handleResize = () => {
      setWindowHeight(window.innerHeight);
    };

    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end end"],
  });

  const smoothScrollProgress = useSpring(scrollYProgress, {
    stiffness: 100,
    damping: 30,
    restDelta: 0.001,
  });

  return (
    <div
      ref={containerRef}
      className={cn("relative w-full", contentClassName)}
      style={{ height: `${content.length * 100}vh` }}
    >
      {windowHeight > 0 &&
        content.map((item, index) => (
          <ContentItem
            key={`section-${index}`}
            index={index}
            item={item}
            windowHeight={windowHeight}
            smoothScrollProgress={smoothScrollProgress}
            contentLength={content.length}
          />
        ))}
    </div>
  );
};
