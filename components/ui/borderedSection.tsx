import React from "react";
import HighlightedText from "@/components/ui/HighlightedText";

const BorderedSection = ({
  subtitle,
  title,
  text,
  highlightWords = [],
  className = "",
  titleClassName = "mb-8 text-2xl font-light tracking-wider text-white uppercase md:text-3xl",
  textClassName = "mx-auto max-w-2xl text-sm leading-9 text-gray-100 md:text-base",
  showTitle = true,
}: {
  subtitle?: string;
  title?: string;
  text: string;
  highlightWords?: string[];
  className?: string;
  titleClassName?: string;
  textClassName?: string;
  showTitle?: boolean;
}) => {
  return (
    <div
      className={`rounded-[4rem] border-[1.5rem] border-[#0D1641] px-8 py-16 text-center ${className}`}
    >
      {showTitle && title && <h2 className={titleClassName}>{title}</h2>}
      {showTitle && subtitle && !title && (
        <h2 className={titleClassName}>{subtitle}</h2>
      )}
      <div className={textClassName}>
        <HighlightedText text={text} highlightWords={highlightWords} />
      </div>
    </div>
  );
};

export default BorderedSection;
