import React, { useEffect, useRef, useMemo } from "react";
import { motion, useInView } from "motion/react";
import { products } from "@/constants/products";
import Image from "next/image";

// Define types for story tile data
interface StoryTile {
  title: string;
  span: string;
  storyDescription: string;
  imageSrc: string;
  imageAlt?: string;
  svgPattern?: React.ReactNode;
}

export default function StoryTiles({
  selectedProduct,
}: {
  selectedProduct: (typeof products)[0];
}) {
  const sectionRef = useRef<HTMLDivElement | null>(null);
  const prevProductRef = useRef(selectedProduct);

  // Only check if in view, with better thresholds
  const isInView = useInView(sectionRef, {
    margin: "-20% 0px -20% 0px",
    once: false,
  });

  // Memoize story data to prevent unnecessary re-renders
  const currentStory: StoryTile = useMemo(
    () => ({
      title: selectedProduct.story.title,
      span: selectedProduct.story.span,
      storyDescription: selectedProduct.story.storyDescription,
      imageSrc: selectedProduct.story.imageSrc,
      imageAlt: selectedProduct.story.imageAlt,
      svgPattern: selectedProduct.story.svgPattern,
    }),
    [selectedProduct],
  );

  // Check if product changed
  const productChanged = prevProductRef.current !== selectedProduct;

  useEffect(() => {
    prevProductRef.current = selectedProduct;
  }, [selectedProduct]);

  // Optimized animation variants - consolidated and simplified
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.1,
      },
    },
    exit: {
      opacity: 0,
      transition: {
        staggerChildren: 0.05,
        staggerDirection: -1,
      },
    },
  };

  const imageVariants = {
    hidden: {
      opacity: 0,
      scale: 0.95,
      filter: "blur(4px)",
    },
    visible: {
      opacity: 1,
      scale: 1,
      filter: "blur(0px)",
      transition: {
        duration: 0.8,
        ease: [0.16, 1, 0.3, 1],
      },
    },
    exit: {
      opacity: 0,
      scale: 1.05,
      filter: "blur(6px)",
      transition: { duration: 0.3 },
    },
  };

  const textVariants = {
    hidden: {
      opacity: 0,
      y: 32,
      filter: "blur(2px)",
    },
    visible: {
      opacity: 1,
      y: 0,
      filter: "blur(0px)",
      transition: {
        duration: 0.6,
        ease: [0.25, 0.46, 0.45, 0.94],
      },
    },
    exit: {
      opacity: 0,
      y: -24,
      filter: "blur(3px)",
      transition: { duration: 0.2 },
    },
  };

  const indicatorVariants = {
    hidden: {
      opacity: 0,
      scaleX: 0,
    },
    visible: {
      opacity: 1,
      scaleX: 1,
      transition: {
        duration: 0.4,
        ease: [0.68, -0.55, 0.265, 1.55],
        delay: 0.3,
      },
    },
    exit: {
      opacity: 0,
      scaleX: 0,
      transition: { duration: 0.15 },
    },
  };

  // Determine animation state based on view and product change
  const animationState = useMemo(() => {
    if (!isInView) return "hidden";
    if (productChanged) return "exit";
    return "visible";
  }, [isInView, productChanged]);

  return (
    <div ref={sectionRef} className="relative w-full overflow-hidden">
      {/* Two-column layout container */}
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate={animationState}
        className="flex min-h-screen w-full flex-col md:flex-row"
      >
        {/* Left column - Image (50% width) */}
        <div className="relative h-[50vh] w-full overflow-hidden md:h-screen md:w-1/2">
          <motion.div
            variants={imageVariants}
            className="absolute inset-0 h-full w-full"
          >
            <Image
              src={currentStory.imageSrc}
              alt={currentStory.imageAlt || `${currentStory.title} story image`}
              width={1920}
              height={1080}
              className="h-full w-full object-cover transition-transform duration-[8s] ease-out hover:scale-105"
              priority={isInView}
              loading={isInView ? "eager" : "lazy"}
            />
          </motion.div>
        </div>

        {/* Right column - Text content (50% width) */}
        <div className="relative flex h-[50vh] w-full items-center md:h-screen md:w-1/2">
          {/* Subtle animated background */}
          <div className="absolute inset-0 opacity-20">
            {currentStory.svgPattern}
          </div>

          <div className="relative z-10 mx-auto max-w-xl px-8 py-12">
            <motion.h2
              variants={textVariants}
              className="mb-4 text-4xl font-bold text-white md:text-5xl"
            >
              {currentStory.title}
            </motion.h2>

            <motion.p
              variants={textVariants}
              className="mb-6 font-serif text-xl text-gray-400 italic md:text-2xl"
            >
              {currentStory.span}
            </motion.p>

            <motion.p
              variants={textVariants}
              className="mb-8 text-lg leading-relaxed text-gray-200"
            >
              {currentStory.storyDescription}
            </motion.p>

            {/* Enhanced product indicator */}
            <div className="mt-12 flex items-center gap-4">
              <motion.div
                variants={indicatorVariants}
                className="h-1.5 w-12 origin-left rounded-full bg-gradient-to-r from-rose-400 to-pink-500"
              />
              <motion.div
                variants={{
                  hidden: { opacity: 0, scaleX: 0 },
                  visible: {
                    opacity: 1,
                    scaleX: 1,
                    transition: {
                      duration: 0.3,
                      ease: "easeOut",
                      delay: 0.5,
                    },
                  },
                  exit: {
                    opacity: 0,
                    scaleX: 0,
                    transition: { duration: 0.1 },
                  },
                }}
              />
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
}
