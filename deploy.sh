#!/bin/bash

# Deploy Script for dev.jewelsofvenus.in
# Logs everything to deploy.log

LOG_FILE="deploy.log"

echo "===========================" >> $LOG_FILE
echo "🚀 Deploy started at: $(date)" >> $LOG_FILE

# Step 1: Reset local changes and clean untracked files
echo "🧹 Cleaning up local changes..." | tee -a $LOG_FILE
git reset --hard HEAD >> $LOG_FILE 2>&1
git clean -fd >> $LOG_FILE 2>&1

# Step 2: Pull latest code from devlopment
echo "🔄 Pulling latest code from devlopment..." | tee -a $LOG_FILE
git pull origin devlopment >> $LOG_FILE 2>&1

# Step 3: Install dependencies
echo "📦 Installing dependencies..." | tee -a $LOG_FILE
npm install >> $LOG_FILE 2>&1

# Step 4: Build the app
echo "🔨 Building the Next.js app..." | tee -a $LOG_FILE
npm run build >> $LOG_FILE 2>&1

# Step 5: Restart PM2 process
echo "🚀 Restarting PM2 process (jov-dev)..." | tee -a $LOG_FILE
pm2 restart jov-dev >> $LOG_FILE 2>&1

# Final log line
echo "✅ Deployment completed at: $(date)" | tee -a $LOG_FILE
echo "===========================" >> $LOG_FILE